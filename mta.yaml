_schema-version: 3.3.0
ID: AI-powered-procurement-assistant
version: 1.0.0
description: "A simple CAP project."
parameters:
  enable-parallel-deployments: true
  deploy_mode: html5-repo
build-parameters:
  before-all:
    - builder: custom
      commands:
        - npm ci
        - npx cds build --production
modules:
  - name: AI-powered-procurement-assistant-srv
    type: nodejs
    path: gen/srv
    parameters:
      instances: 1
      buildpack: nodejs_buildpack
    build-parameters:
      builder: npm-ci
    provides:
      - name: srv-api # required by consumers of CAP services (e.g. approuter)
        properties:
          srv-url: ${default-url}
    requires:
      - name: AI-powered-procurement-assistant-auth
      - name: AI-powered-procurement-assistant-destination

  - name: AI-powered-procurement-assistant
    type: approuter.nodejs
    path: app/router
    parameters:
      keep-existing-routes: true
      disk-quota: 256M
      memory: 256M
    requires:
      - name: srv-api
        group: destinations
        properties:
          name: srv-api # must be used in xs-app.json as well
          url: ~{srv-url}
          forwardAuthToken: true
      - name: AI-powered-procurement-assistant-auth
      - name: AI-powered-procurement-assistant-destination
      - name: AI-powered-procurement-assistant-html5-runtime
    provides:
      - name: app-api
        properties:
          app-protocol: ${protocol}
          app-uri: ${default-uri}

  - name: AI-powered-procurement-assistant-app-deployer
    type: com.sap.application.content
    path: gen
    requires:
      - name: AI-powered-procurement-assistant-html5-repo-host
        parameters:
          content-target: true
    build-parameters:
      build-result: app/
      requires:
        - name: AIpoweredprocurementassistantadminbooks
          artifacts:
            - admin-books.zip
          target-path: app/
        - name: AIpoweredprocurementassistantbrowse
          artifacts:
            - browse.zip
          target-path: app/

  - name: AIpoweredprocurementassistantadminbooks
    type: html5
    path: app/admin-books
    build-parameters:
      build-result: dist
      builder: custom
      commands:
        - npm ci
        - npm run build
      supported-platforms:
        []
  - name: AIpoweredprocurementassistantbrowse
    type: html5
    path: app/browse
    build-parameters:
      build-result: dist
      builder: custom
      commands:
        - npm ci
        - npm run build
      supported-platforms:
        []

resources:
  - name: AI-powered-procurement-assistant-auth
    type: org.cloudfoundry.managed-service
    parameters:
      service: xsuaa
      service-plan: application
      path: ./xs-security.json
      config:
        xsappname: AI-powered-procurement-assistant-${org}-${space}
        tenant-mode: dedicated
        oauth2-configuration:
          redirect-uris:
            - https://*~{app-api/app-uri}/**
    requires:
      - name: app-api
  - name: AI-powered-procurement-assistant-destination
    type: org.cloudfoundry.managed-service
    parameters:
      service: destination
      service-plan: lite
      config:
        HTML5Runtime_enabled: true
        init_data:
          instance:
            existing_destinations_policy: update
            destinations:
              - Name: srv-api
                URL: ~{srv-api/srv-url}
                Authentication: NoAuthentication
                Type: HTTP
                ProxyType: Internet
                HTML5.ForwardAuthToken: true
                HTML5.DynamicDestination: true
              - Name: ui5
                URL: https://ui5.sap.com
                Authentication: NoAuthentication
                Type: HTTP
                ProxyType: Internet

  - name: AI-powered-procurement-assistant-html5-repo-host
    type: org.cloudfoundry.managed-service
    parameters:
      service: html5-apps-repo
      service-plan: app-host
  - name: AI-powered-procurement-assistant-html5-runtime
    type: org.cloudfoundry.managed-service
    parameters:
      service: html5-apps-repo
      service-plan: app-runtime
