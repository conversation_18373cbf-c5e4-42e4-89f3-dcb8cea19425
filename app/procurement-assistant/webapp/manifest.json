{"_version": "1.49.0", "sap.app": {"id": "procurement.assistant", "applicationVersion": {"version": "1.0.0"}, "type": "application", "title": "AI-Powered Procurement Assistant", "description": "Intelligent procurement management with AI-powered chat assistant", "i18n": "i18n/i18n.properties", "dataSources": {"ProcurementService": {"uri": "odata/v4/procurement/", "type": "OData", "settings": {"odataVersion": "4.0"}}}, "crossNavigation": {"inbounds": {"procurement-display": {"signature": {"parameters": {}, "additionalParameters": "allowed"}, "semanticObject": "Procurement", "action": "display", "title": "AI-Powered Procurement Assistant", "info": "Intelligent procurement management", "subTitle": "Chat • Warehouse • Suppliers", "icon": "sap-icon://business-suite"}}}}, "sap.ui": {"technology": "UI5", "fullWidth": true, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"dependencies": {"minUI5Version": "1.115.1", "libs": {"sap.m": {}, "sap.ui.core": {}, "sap.ui.layout": {}, "sap.f": {}, "sap.suite.ui.commons": {}, "sap.ui.unified": {}}}, "models": {"i18n": {"type": "sap.ui.model.resource.ResourceModel", "uri": "i18n/i18n.properties"}, "": {"dataSource": "ProcurementService", "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true, "groupProperties": {"default": {"submit": "Auto"}}}}}, "routing": {"config": {"routerClass": "sap.m.routing.Router", "viewType": "XML", "path": "procurement.assistant.view", "controlId": "app", "controlAggregation": "pages", "async": true}, "routes": [{"pattern": "", "name": "welcome", "target": "welcome"}, {"pattern": "main", "name": "main", "target": "main"}], "targets": {"welcome": {"viewId": "welcome", "viewName": "Welcome"}, "main": {"viewId": "main", "viewName": "Main"}}}, "contentDensities": {"compact": true, "cozy": true}}, "sap.fiori": {"registrationIds": [], "archeType": "transactional"}}