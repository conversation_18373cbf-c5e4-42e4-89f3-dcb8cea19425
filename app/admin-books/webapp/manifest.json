{"_version": "1.49.0", "sap.app": {"id": "AI-powered-procurement-assistant.admin-books", "applicationVersion": {"version": "1.0.0"}, "type": "application", "title": "{{appTitle}}", "description": "{{appDescription}}", "i18n": "i18n/i18n.properties", "dataSources": {"AdminService": {"uri": "odata/v4/admin/", "type": "OData", "settings": {"odataVersion": "4.0"}}}, "crossNavigation": {"inbounds": {"intent-Books-manage": {"signature": {"parameters": {}, "additionalParameters": "allowed"}, "semanticObject": "Books", "action": "manage"}}}}, "sap.ui": {"technology": "UI5", "fullWidth": false, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"dependencies": {"minUI5Version": "1.115.1", "libs": {"sap.fe.templates": {}}}, "models": {"i18n": {"type": "sap.ui.model.resource.ResourceModel", "uri": "i18n/i18n.properties"}, "": {"dataSource": "AdminService", "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true, "groupProperties": {"default": {"submit": "Auto"}}}}}, "routing": {"routes": [{"pattern": ":?query:", "name": "BooksList", "target": "BooksList"}, {"pattern": "Books({key}):?query:", "name": "BooksDetails", "target": "BooksDetails"}, {"pattern": "Books({key}/author({key2}):?query:", "name": "AuthorsDetails", "target": "AuthorsDetails"}], "targets": {"BooksList": {"type": "Component", "id": "BooksList", "name": "sap.fe.templates.ListReport", "options": {"settings": {"entitySet": "Books", "initialLoad": true, "navigation": {"Books": {"detail": {"route": "BooksDetails"}}}}}}, "BooksDetails": {"type": "Component", "id": "BooksDetailsList", "name": "sap.fe.templates.ObjectPage", "options": {"settings": {"entitySet": "Books", "editableHeaderContent": false, "navigation": {"Authors": {"detail": {"route": "AuthorsDetails"}}}}}}, "AuthorsDetails": {"type": "Component", "id": "AuthorsDetailsList", "name": "sap.fe.templates.ObjectPage", "options": {"settings": {"entitySet": "Authors"}}}}}, "contentDensities": {"compact": true, "cozy": true}}, "sap.fiori": {"registrationIds": [], "archeType": "transactional"}}