{"_version": "1.49.0", "sap.app": {"id": "AI-powered-procurement-assistant.browse", "applicationVersion": {"version": "1.0.0"}, "type": "application", "title": "{{appTitle}}", "description": "{{appDescription}}", "i18n": "i18n/i18n.properties", "dataSources": {"CatalogService": {"uri": "odata/v4/catalog/", "type": "OData", "settings": {"odataVersion": "4.0"}}}, "crossNavigation": {"inbounds": {"Books-display": {"signature": {"parameters": {"Books.ID": {"renameTo": "ID"}, "Authors.books.ID": {"renameTo": "ID"}}, "additionalParameters": "allowed"}, "semanticObject": "Books", "action": "display", "title": "{{appTitle}}", "info": "{{appInfo}}", "subTitle": "{{appSubTitle}}", "icon": "sap-icon://course-book", "indicatorDataSource": {"dataSource": "CatalogService", "path": "Books/$count", "refresh": 1800}}}}}, "sap.ui": {"technology": "UI5", "fullWidth": false, "deviceTypes": {"desktop": true, "tablet": true, "phone": true}}, "sap.ui5": {"dependencies": {"minUI5Version": "1.115.1", "libs": {"sap.fe.templates": {}}}, "models": {"i18n": {"type": "sap.ui.model.resource.ResourceModel", "uri": "i18n/i18n.properties"}, "": {"dataSource": "CatalogService", "settings": {"operationMode": "Server", "autoExpandSelect": true, "earlyRequests": true, "groupProperties": {"default": {"submit": "Auto"}}}}}, "routing": {"routes": [{"pattern": ":?query:", "name": "BooksList", "target": "BooksList"}, {"pattern": "Books({key}):?query:", "name": "BooksDetails", "target": "BooksDetails"}], "targets": {"BooksList": {"type": "Component", "id": "BooksList", "name": "sap.fe.templates.ListReport", "options": {"settings": {"entitySet": "Books", "initialLoad": true, "navigation": {"Books": {"detail": {"route": "BooksDetails"}}}}}}, "BooksDetails": {"type": "Component", "id": "BooksDetailsList", "name": "sap.fe.templates.ObjectPage", "options": {"settings": {"entitySet": "Books"}}}}}, "contentDensities": {"compact": true, "cozy": true}}, "sap.fiori": {"registrationIds": [], "archeType": "transactional"}}