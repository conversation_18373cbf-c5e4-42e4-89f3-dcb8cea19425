{"services": {"LaunchPage": {"adapter": {"config": {"catalogs": [], "groups": [{"id": "Bookshop", "title": "Bookshop", "isPreset": true, "isVisible": true, "isGroupLocked": false, "tiles": [{"id": "BrowseBooks", "tileType": "sap.ushell.ui.tile.StaticTile", "properties": {"title": "Browse Books", "targetURL": "#Books-display"}}]}, {"id": "Administration", "title": "Administration", "isPreset": true, "isVisible": true, "isGroupLocked": false, "tiles": [{"id": "ManageBooks", "tileType": "sap.ushell.ui.tile.StaticTile", "properties": {"title": "Manage Books", "targetURL": "#Books-manage"}}]}]}}}, "NavTargetResolution": {"config": {"enableClientSideTargetResolution": true}}, "ClientSideTargetResolution": {"adapter": {"config": {"inbounds": {"BrowseBooks": {"semanticObject": "Books", "action": "display", "title": "Browse Books", "signature": {"parameters": {"Books.ID": {"renameTo": "ID"}, "Authors.books.ID": {"renameTo": "ID"}}, "additionalParameters": "ignored"}, "resolutionResult": {"applicationType": "SAPUI5", "additionalInformation": "SAPUI5.Component=bookshop", "url": "browse/webapp"}}, "ManageBooks": {"semanticObject": "Books", "action": "manage", "title": "Manage Books", "signature": {"parameters": {}, "additionalParameters": "allowed"}, "resolutionResult": {"applicationType": "SAPUI5", "additionalInformation": "SAPUI5.Component=books", "url": "admin-books/webapp"}}}}}}}}